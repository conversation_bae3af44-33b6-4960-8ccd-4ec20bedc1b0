<?php
namespace Webguru\LandingPageImport\Controller\Adminhtml\Import;

use Magento\Backend\App\Action;
use Magento\Framework\File\Csv;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\App\ResourceConnection;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Cms\Api\PageRepositoryInterface;
use Magento\Cms\Api\Data\PageInterfaceFactory;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;
use Webguru\Utils\Helper\Data as Helper;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\Product\Action as ProductAction;

class Upload extends Action
{
    protected $csv;
    protected $filesystem;
    protected $dateTime;
    protected $resource;
    protected $productRepository;
    protected $storeManager;
    protected $pageRepository;
    protected $pageFactory;
    protected $uploaderFactory;
    protected $logger;
    protected $helper;
    protected $productCollectionFactory;
    protected $productAction;

    public function __construct(
        Action\Context $context,
        Csv $csv,
        Filesystem $filesystem,
        DateTime $dateTime,
        ResourceConnection $resource,
        ProductRepositoryInterface $productRepository,
        StoreManagerInterface $storeManager,
        PageRepositoryInterface $pageRepository,
        PageInterfaceFactory $pageFactory,
        UploaderFactory $uploaderFactory,
        LoggerInterface $logger,
        Helper $helper,
        CollectionFactory $productCollectionFactory,
        ProductAction $productAction
    ) {
        parent::__construct($context);
        $this->csv = $csv;
        $this->filesystem = $filesystem;
        $this->dateTime = $dateTime;
        $this->resource = $resource;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->pageRepository = $pageRepository;
        $this->pageFactory = $pageFactory;
        $this->uploaderFactory = $uploaderFactory;
        $this->logger = $logger;
        $this->helper = $helper;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->productAction = $productAction;
    }

    public function execute()
    {
        try {
            $this->logger->info('Landing Page Import started.');
            $uploader = $this->uploaderFactory->create(['fileId' => 'import_file']);
            $uploader->setAllowedExtensions(['csv']);
            $uploader->setAllowRenameFiles(true);
            $uploader->setFilesDispersion(false);
            $path = $this->filesystem->getDirectoryRead(DirectoryList::VAR_DIR)->getAbsolutePath('import/');
            $result = $uploader->save($path);

            $csvData = $this->csv->getData($result['path'] . $result['file']);
            unset($csvData[0]);

            $new = $existing = 0;

            foreach ($csvData as $row) {
                $i = 0;
                $pageTitle = mb_convert_encoding(trim($row[$i++]), 'UTF-8') ?? 'DTTM Landing Page';
                $contentHeading = mb_convert_encoding(trim($row[$i++]), 'UTF-8') ?? '';
                $content = mb_convert_encoding($row[$i++], 'UTF-8') ?? '';
                $contentMiddle = mb_convert_encoding($row[$i++], 'UTF-8') ?? '';
                $contentMiddle1 = mb_convert_encoding($row[$i++], 'UTF-8') ?? '';
                $contentMiddle2 = mb_convert_encoding($row[$i++], 'UTF-8') ?? '';
                $contentAfter = mb_convert_encoding($row[$i++], 'UTF-8') ?? '';
                $urlKey = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', trim($row[$i++]))) ?? '';
                $metaTitle = mb_convert_encoding($row[$i++], 'UTF-8') ?? '';
                $metaDescription = mb_convert_encoding($row[$i++], 'UTF-8') ?? '';
                $skuList = array_filter(array_map('trim', explode(',', trim($row[$i++]) ?? '')));
                $sizeList = array_filter(array_map('trim', explode(' ', trim($row[$i++]) ?? '')));
                $backlink = mb_convert_encoding(trim($row[$i++]), 'UTF-8') ?? '';
                $schema = mb_convert_encoding(trim($row[$i++]), 'UTF-8') ?? '';
                $internalLink = mb_convert_encoding(trim($row[$i++]), 'UTF-8') ?? '';
                $parentId = mb_convert_encoding(trim($row[$i++]), 'UTF-8') ?? null;

                if ($urlKey=='') continue;

                // Parse $sku as an array (comma-separated SKUs)

                // Add PageBuilder Products element at the end of content if SKU is present
                $fullContent = '<style>#html-body [data-pb-style=B1QJG96]{justify-content:flex-start;display:flex;flex-direction:column;background-position:left top;background-size:cover;background-repeat:no-repeat;background-attachment:scroll}#html-body [data-pb-style=YPCL3Q8]{text-align:center}#html-body [data-pb-style=HM6QDVE]{margin-bottom:30px}</style><div data-content-type="row" data-appearance="contained" data-element="main"><div data-enable-parallax="0" data-parallax-speed="0.5" data-background-images="{}" data-background-type="image" data-video-loop="true" data-video-play-only-visible="true" data-video-lazy-load="true" data-video-fallback-src="" data-element="inner" data-pb-style="B1QJG96">';

                if ($content) {
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main">'.$content.'</div>';
                }
                if (!empty($skuList)) {
                    $skuString = implode(',', $skuList);
                    $fullContent .= '<div data-content-type="products" data-appearance="grid" data-element="main" data-pb-style="YPCL3Q8">{{widget type="Magento\CatalogWidget\Block\Product\ProductsList" template="Magento_CatalogWidget::product/widget/content/grid.phtml" anchor_text="" id_path="" show_pager="0" products_count="5" condition_option="sku" condition_option_value="' . $skuString . '" type_name="Catalog Products List" conditions_encoded="^[`1`:^[`aggregator`:`all`,`new_child`:``,`type`:`Magento||CatalogWidget||Model||Rule||Condition||Combine`,`value`:`1`^],`1--1`:^[`operator`:`()`,`type`:`Magento||CatalogWidget||Model||Rule||Condition||Product`,`attribute`:`sku`,`value`:`' . $skuString . '`^]^]" sort_order="position_by_sku"}}</div>';
                }
                if (!empty($sizeList)) {
                    $width = $this->helper->getOptionId("tire_width", (string) $sizeList[0] ?? '210');
                    $ratio = $this->helper->getOptionId("tire_aspect_ratio", (string) $sizeList[1] ?? '60');
                    $rim = $this->helper->getOptionId("tire_rim_diameter", (string) $sizeList[2] ?? '16');

                    $fullContent .= '<div data-content-type="products" data-appearance="grid" data-element="main" data-pb-style="X9K88R9">{{widget type="Magento\CatalogWidget\Block\Product\ProductsList" template="Magento_CatalogWidget::product/widget/content/grid.phtml" anchor_text="" id_path="" show_pager="0" products_count="8" condition_option="condition" condition_option_value="" type_name="Catalog Products List" conditions_encoded="^[`1`:^[`type`:`Magento||CatalogWidget||Model||Rule||Condition||Combine`,`aggregator`:`all`,`value`:`1`,`new_child`:``^],`1--1`:^[`type`:`Magento||CatalogWidget||Model||Rule||Condition||Product`,`attribute`:`tire_width`,`operator`:`==`,`value`:`'.$width.'`^],`1--2`:^[`type`:`Magento||CatalogWidget||Model||Rule||Condition||Product`,`attribute`:`tire_aspect_ratio`,`operator`:`==`,`value`:`'.$ratio.'`^],`1--3`:^[`type`:`Magento||CatalogWidget||Model||Rule||Condition||Product`,`attribute`:`tire_rim_diameter`,`operator`:`==`,`value`:`'.$rim.'`^]^]" sort_order="price_low_to_high"}}</div>';

                    $searchUrl = '/tires?tire_width='.$width.'&tire_aspect_ratio='.$ratio.'&tire_rim_diameter='.$rim;
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main"><p style="font-style: italic;text-align: center;margin-bottom: 30px;background: #7c0910;padding: 5px;"><a href="'.$searchUrl.'" style="color: white;font-weight: bold;">Click Here to View All Available Tires in This Size and Filter by Category</a></p></div>';
                }
                if ($contentMiddle) {
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main" data-pb-style="HM6QDVE">'.$contentMiddle.'</div>';
                }

                if ($parentId) {
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main">{{block class="Mageside\PageHierarchy\Block\PageHierarchy" template="Mageside_PageHierarchy::left/hierarchy.phtml"}}</div>';
                }
                
                if ($contentMiddle1) {
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main" data-pb-style="HM6QDVE">'.$contentMiddle1.'</div>';
                }
                if ($contentMiddle2) {
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main" data-pb-style="HM6QDVE">'.$contentMiddle2.'</div>';
                }
                if ($contentAfter) {
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main">'.$contentAfter.'</div>';
                }
                if ($internalLink) {
                    $fullContent .= '<div data-content-type="text" data-appearance="default" data-element="main">'.$internalLink.'</div>';
                }

                if ($schema) {
                    $fullContent .= $schema;
                }

                $fullContent .= '</div></div>';

                
                // Create CMS Page
                // Try to load existing page by identifier
                try {
                    $cmsPage = $this->pageRepository->getById($urlKey);
                    $existing++;
                    $this->logger->info("Updated existing page: $urlKey");
                } catch (NoSuchEntityException $e) {
                    $cmsPage = $this->pageFactory->create();
                    $new++;
                    $cmsPage->setIdentifier($urlKey);
                    $this->logger->info("Created new page: $urlKey");
                }
                
                $cmsPage->setTitle($pageTitle)
                ->setContentHeading($contentHeading)
                ->setContent($fullContent)
                ->setMetaTitle($metaTitle)
                ->setMetaDescription($metaDescription)
                //->setMetaKeywords($metaKeywords)
                ->setIsActive(1)
                ->setPageLayout('2columns-right')
                ->setStores([0]); // 0 = All Stores
                
                if (!empty($parentId)) {
                    try {
                        $parentPage = $this->pageRepository->getById($parentId);
                        $parentPageId = $parentPage->getId();
                        $cmsPage->setData('parent_page_id', $parentPageId);
                    } catch (NoSuchEntityException $e) {
                        
                    }
                }
                $cmsPage->setData('show_menu_hierarchy', true);
                $cmsPage->setData('include_in_menu_hierarchy', true);
                
                $this->pageRepository->save($cmsPage);

                // Update landing_page attribute for the products with $skuList
                if (!empty($skuList) && $backlink) {
                    foreach ($skuList as $singleSku) {
                        try {
                            $product = $this->productRepository->get($singleSku);

                            // Replace %word% with HTML link
                            $backlinkHtml = preg_replace_callback('/%([^%]+)%/', function ($matches) use ($urlKey) {
                                return '<a href="' . $urlKey . '">' . htmlspecialchars($matches[1]) . '</a>';
                            }, $backlink);

                            $product->setData('landing_page', $backlinkHtml);
                            $this->productRepository->save($product);
                            //$this->logger->info("Updated product $singleSku with landing page backlink.");
                        } catch (\Exception $e) {
                            // Optionally log or handle product not found or save errors
                            //$this->logger->error("Error updating product $singleSku: " . $e->getMessage());
                        }
                    }
                }

                // Update landing_page attribute for products matching sizeList
                if (!empty($sizeList) && $backlink) {
                    try {
                        $width = $this->helper->getOptionId("tire_width", (string) $sizeList[0] ?? '210');
                        $ratio = $this->helper->getOptionId("tire_aspect_ratio", (string) $sizeList[1] ?? '60');
                        $rim = $this->helper->getOptionId("tire_rim_diameter", (string) $sizeList[2] ?? '16');

                        $productCollection = $this->productCollectionFactory->create();
                        $productCollection->addAttributeToFilter('tire_width', $width)
                            ->addAttributeToFilter('tire_aspect_ratio', $ratio)
                            ->addAttributeToFilter('tire_rim_diameter', $rim);

                        // Replace %word% with HTML link
                        $backlinkHtml = preg_replace_callback('/%([^%]+)%/', function ($matches) use ($urlKey) {
                            return '<a href="' . $urlKey . '">' . htmlspecialchars($matches[1]) . '</a>';
                        }, $backlink);

                        /*foreach ($productCollection as $product) {
                            try {
                                $product->setData('landing_page', $backlinkHtml);
                                $product->getResource()->save($product);
                                $this->logger->info("Updated product " . $product->getSku() . " with landing page backlink (size match).");
                            } catch (\Exception $e) {
                                $this->logger->error("Error updating product " . $product->getSku() . ": " . $e->getMessage());
                            }
                        }*/

                        $this->productAction->updateAttributes(
                            $productCollection->getAllIds(),   // Array of product IDs
                            ['landing_page' => $backlinkHtml], // Attribute(s) to update
                            0 // Store ID (0 = default)
                        );
                        //$this->logger->info("Updated products " . print_r($productCollection->getAllIds(), true) . " with landing page backlink (size match).");
                    } catch (\Exception $e) {
                        //$this->logger->error("Error updating products by size: " . $e->getMessage());
                    }
                }
            }

            $this->messageManager->addSuccessMessage(__('CSV Imported Successfully.'));
            if ($new) $this->messageManager->addSuccessMessage($new.__(' New Pages Created.'));
            if ($existing) $this->messageManager->addSuccessMessage($existing.__(' Existing Pages Updated.'));

            $this->logger->info("Import finished. New: $new, Existing: $existing");

        } catch (\Exception $e) {
            $this->logger->error('Import error: ' . $e->getMessage());
            $this->messageManager->addErrorMessage(__('Error: ') . $e->getMessage());
        }

        return $this->_redirect('landingpageimport/import/index');
    }
}
